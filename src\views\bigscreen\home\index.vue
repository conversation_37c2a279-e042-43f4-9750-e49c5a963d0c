<script setup lang="ts">
import { useBigscreenTheme } from '@/layouts/bigscreen-layout/theme/index';

defineOptions({
  name: 'BigscreenHome'
});

const { bigscreenTheme } = useBigscreenTheme();

// 示例数据
const leftData = [
  { title: '实时访问量', value: '12,580', unit: '人次', trend: '+12.5%' },
  { title: '在线用户数', value: '3,420', unit: '人', trend: '+8.2%' },
  { title: '系统负载', value: '68.5', unit: '%', trend: '-2.1%' }
];

const rightData = [
  { title: '今日订单', value: '8,960', unit: '单', trend: '+15.3%' },
  { title: '销售额', value: '156.8', unit: '万', trend: '+22.1%' },
  { title: '转化率', value: '12.8', unit: '%', trend: '+5.6%' }
];
</script>

<template>
  <div class="bigscreen-home h-full w-full">
    <!-- 主体内容区 - 占满整个容器 -->
    <main class="h-full w-full flex gap-4 p-4">
      <!-- 左侧三个容器 -->
      <section class="min-w-60 w-1/5 flex flex-col gap-4">
        <div
          v-for="(item, index) in leftData"
          :key="index"
          class="bigscreen-card rounded-lg p-4 transition-all hover:scale-105"
          :class="{ 'flex-1': index === 2, 'h-32': index !== 2 }"
        >
          <h3 class="mb-2 text-sm font-medium" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
            {{ item.title }}
          </h3>
          <div class="flex items-end justify-between">
            <div>
              <span class="text-2xl font-bold" :style="{ color: bigscreenTheme.colors.textColor }">
                {{ item.value }}
              </span>
              <span class="ml-1 text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
                {{ item.unit }}
              </span>
            </div>
            <span
              class="text-xs"
              :style="{
                color: item.trend.startsWith('+')
                  ? bigscreenTheme.colors.successColor
                  : bigscreenTheme.colors.errorColor
              }"
            >
              {{ item.trend }}
            </span>
          </div>
        </div>
      </section>

      <!-- 中间区域 -->
      <section class="min-w-0 flex flex-col flex-1 gap-4">
        <div class="bigscreen-card h-2/5 flex items-center justify-center rounded-lg p-6">
          <!-- 地图区域 -->
          <div class="text-center">
            <NIcon size="64" :style="{ color: bigscreenTheme.colors.buttonBg }">
              <SvgIcon icon="mdi:map" />
            </NIcon>
            <p class="mt-4 text-xl font-semibold" :style="{ color: bigscreenTheme.colors.textColor }">地图区域</p>
            <p class="mt-2 text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
              这里可以放置地图组件或其他可视化图表
            </p>
          </div>
        </div>
        <div class="bigscreen-card flex-1 rounded-lg p-6">
          <div class="h-full flex items-center justify-center text-center">
            <div>
              <NIcon size="64" :style="{ color: bigscreenTheme.colors.infoColor }">
                <SvgIcon icon="mdi:chart-line" />
              </NIcon>
              <p class="mt-4 text-xl font-semibold" :style="{ color: bigscreenTheme.colors.textColor }">数据图表区域</p>
              <p class="mt-2 text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
                这里可以放置各种数据图表和统计信息
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- 右侧三个容器 -->
      <section class="min-w-60 w-1/5 flex flex-col gap-4">
        <div
          v-for="(item, index) in rightData"
          :key="index"
          class="bigscreen-card rounded-lg p-4 transition-all hover:scale-105"
          :class="{ 'flex-1': index === 2, 'h-32': index !== 2 }"
        >
          <h3 class="mb-2 text-sm font-medium" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
            {{ item.title }}
          </h3>
          <div class="flex items-end justify-between">
            <div>
              <span class="text-2xl font-bold" :style="{ color: bigscreenTheme.colors.textColor }">
                {{ item.value }}
              </span>
              <span class="ml-1 text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
                {{ item.unit }}
              </span>
            </div>
            <span class="text-xs">
              {{ item.trend }}
            </span>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<style scoped>
.bigscreen-home {
  /* 确保占满整个容器 */
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.bigscreen-card {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  /* 添加科技感边框发光效果 */
  border: 1px solid v-bind('bigscreenTheme.colors.borderColor');
  position: relative;
}

.bigscreen-card::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  /* background: linear-gradient(45deg, transparent 30%, v-bind('bigscreenTheme.colors.borderColor') 50%, transparent 70%); */
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.bigscreen-card:hover::before {
  opacity: 0.3;
}

.bigscreen-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: v-bind('bigscreenTheme.shadows.dialog');
  border-color: v-bind('bigscreenTheme.colors.successColor');
}

/* 数据数字的发光效果 */
.bigscreen-card .text-2xl {
  text-shadow: 0 0 10px v-bind('bigscreenTheme.colors.buttonBg');
}

/* 趋势指示器的动画效果 */
.bigscreen-card .text-xs {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .bigscreen-home main {
    flex-direction: column;
    gap: 2rem;
  }

  .bigscreen-home section {
    width: 100%;
    min-width: auto;
  }

  .bigscreen-home .min-w-60 {
    min-width: auto;
  }
}
</style>
